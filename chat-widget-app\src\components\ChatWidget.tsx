'use client';

import { useState, useEffect, useRef } from 'react';

interface Message {
  id: number;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

interface ChatSession {
  sessionId: string;
  userId: string;
  startTime: Date;
  lastActivity: Date;
}



export default function ChatWidget() {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: 1,
      text: "Hello! I'm <PERSON> White AI assistant. How can I help you with your property needs today?",
      isUser: false,
      timestamp: new Date()
    }
  ]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [session] = useState<ChatSession>(() => {
    const userId = `user-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    const sessionId = `session-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    return {
      sessionId,
      userId,
      startTime: new Date(),
      lastActivity: new Date()
    };
  });
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // N8n webhook configuration
  const N8N_WEBHOOK_URL = 'https://raywhiteltd.app.n8n.cloud/webhook/84fc3ca5-f11a-41f5-b520-c5a22c8c7a05/chat';
  const ROUTE = 'general';

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const sendToN8n = async (message: string) => {
    try {
      setIsLoading(true);

      const payload = [
        {
          action: "sendMessage",
          sessionId: session.sessionId,
          route: ROUTE,
          chatInput: message,
          metadata: {
            timestamp: new Date().toISOString(),
            userId: session.userId,
            sessionStartTime: session.startTime.toISOString()
          }
        }
      ];

      console.log('Sending to n8n:', { url: N8N_WEBHOOK_URL, payload });

      const response = await fetch(N8N_WEBHOOK_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      });

      console.log('n8n response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('n8n error response:', errorText);
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      // Get response as text first
      const responseText = await response.text();
      console.log('n8n raw response:', responseText);

      let botResponseText = '';

      try {
        // Try to parse as JSON first
        const data = JSON.parse(responseText);
        console.log('n8n parsed JSON data:', data);

        // Handle the array response format
        if (Array.isArray(data) && data.length > 0 && data[0].output) {
          botResponseText = data[0].output;
        } else if (data.output) {
          // Handle single object format
          botResponseText = data.output;
        } else {
          console.warn('Unexpected JSON response format from n8n:', data);
          botResponseText = "I received an unexpected response format. Please try again.";
        }
      } catch (jsonError) {
        // If JSON parsing fails, treat the response as plain text
        console.log('Response is not JSON, treating as plain text:', responseText);
        botResponseText = responseText.trim();
      }

      if (botResponseText) {
        const botResponse: Message = {
          id: Date.now(),
          text: botResponseText,
          isUser: false,
          timestamp: new Date()
        };

        setMessages(prev => [...prev, botResponse]);
      } else {
        const errorMessage: Message = {
          id: Date.now(),
          text: "I received an empty response. Please try again.",
          isUser: false,
          timestamp: new Date()
        };
        setMessages(prev => [...prev, errorMessage]);
      }
    } catch (error) {
      console.error('Error sending message to n8n:', error);

      // Show error message to user
      const errorMessage: Message = {
        id: Date.now(),
        text: "Sorry, I'm having trouble connecting right now. Please try again later.",
        isUser: false,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (inputText.trim() === '' || isLoading) return;

    const userMessage: Message = {
      id: Date.now(),
      text: inputText,
      isUser: true,
      timestamp: new Date()
    };

    // Add user message immediately
    setMessages(prev => [...prev, userMessage]);
    const messageToSend = inputText;
    setInputText('');

    // Send to n8n and wait for response
    await sendToN8n(messageToSend);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {/* Chat Button */}
      {!isOpen && (
        <button
          onClick={() => setIsOpen(true)}
          className="bg-blue-900 text-white rounded-full w-16 h-16 flex items-center justify-center shadow-xl hover:bg-blue-800 transition-all duration-300 transform hover:scale-105"
          title="Chat with Ray White AI"
        >
          <svg
            className="w-7 h-7"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
            />
          </svg>
        </button>
      )}

      {/* Chat Window */}
      {isOpen && (
        <div className="bg-white border border-gray-200 rounded-xl shadow-2xl w-96 h-[500px] flex flex-col overflow-hidden">
          {/* Header */}
          <div className="bg-blue-900 text-white p-4 flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <img
                src="https://raywhiteltd.com/_next/static/media/logo.60ee6487.svg"
                alt="Ray White Logo"
                className="w-8 h-8 bg-white rounded p-1"
              />
              <h3 className="font-semibold text-lg">Ray White AI</h3>
            </div>
            <button
              onClick={() => setIsOpen(false)}
              className="text-white hover:text-gray-300 transition-colors p-1"
              title="Close chat"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          {/* Messages */}
          <div className="flex-1 p-4 overflow-y-auto space-y-3 bg-gray-50">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs px-4 py-3 rounded-2xl text-sm shadow-sm ${
                    message.isUser
                      ? 'bg-blue-900 text-white rounded-br-md'
                      : 'bg-white text-gray-800 border border-gray-200 rounded-bl-md'
                  }`}
                >
                  {message.text}
                </div>
              </div>
            ))}

            {/* Loading indicator */}
            {isLoading && (
              <div className="flex justify-start">
                <div className="bg-white text-gray-800 border border-gray-200 max-w-xs px-4 py-3 rounded-2xl rounded-bl-md text-sm shadow-sm">
                  <div className="flex items-center space-x-2">
                    <span className="text-xs text-gray-500">Ray White AI is typing</span>
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-blue-900 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-blue-900 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                      <div className="w-2 h-2 bg-blue-900 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Input */}
          <div className="p-4 border-t border-gray-200 bg-white">
            <div className="flex space-x-3">
              <input
                type="text"
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Ask about properties, locations, or services..."
                disabled={isLoading}
                className="flex-1 px-4 py-3 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-900 focus:border-transparent text-sm disabled:opacity-50 bg-gray-50"
              />
              <button
                onClick={handleSendMessage}
                disabled={isLoading || inputText.trim() === ''}
                className="bg-blue-900 text-white p-3 rounded-full hover:bg-blue-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
                title="Send message"
              >
                {isLoading ? (
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  <svg
                    className="w-5 h-5 transform rotate-45"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                  </svg>
                )}
              </button>
            </div>

            {/* Session Info */}
            <div className="mt-2 text-xs text-gray-400 text-center">
              Session: {session.sessionId.split('-')[1]}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
