{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/R<PERSON>an/CW%20manual%20edit%20from%20github/chat-widget-app/src/components/ChatWidget.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\n\ninterface Message {\n  id: number;\n  text: string;\n  isUser: boolean;\n  timestamp: Date;\n}\n\n\n\nexport default function ChatWidget() {\n  const [isOpen, setIsOpen] = useState(false);\n  const [messages, setMessages] = useState<Message[]>([\n    {\n      id: 1,\n      text: \"Hello! How can I help you today?\",\n      isUser: false,\n      timestamp: new Date()\n    }\n  ]);\n  const [inputText, setInputText] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [sessionId] = useState(() => `session-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  // N8n webhook configuration\n  const N8N_WEBHOOK_URL = 'https://raywhiteltd.app.n8n.cloud/webhook/84fc3ca5-f11a-41f5-b520-c5a22c8c7a05/chat';\n  const ROUTE = 'general';\n\n  // Auto-scroll to bottom when new messages arrive\n  useEffect(() => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  }, [messages]);\n\n  const sendToN8n = async (message: string) => {\n    try {\n      setIsLoading(true);\n\n      const payload = [\n        {\n          action: \"sendMessage\",\n          sessionId: sessionId,\n          route: ROUTE,\n          chatInput: message,\n          metadata: {\n            timestamp: new Date().toISOString()\n          }\n        }\n      ];\n\n      console.log('Sending to n8n:', { url: N8N_WEBHOOK_URL, payload });\n\n      const response = await fetch(N8N_WEBHOOK_URL, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(payload)\n      });\n\n      console.log('n8n response status:', response.status);\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error('n8n error response:', errorText);\n        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);\n      }\n\n      // Get response as text first\n      const responseText = await response.text();\n      console.log('n8n raw response:', responseText);\n\n      let botResponseText = '';\n\n      try {\n        // Try to parse as JSON first\n        const data = JSON.parse(responseText);\n        console.log('n8n parsed JSON data:', data);\n\n        // Handle the array response format\n        if (Array.isArray(data) && data.length > 0 && data[0].output) {\n          botResponseText = data[0].output;\n        } else if (data.output) {\n          // Handle single object format\n          botResponseText = data.output;\n        } else {\n          console.warn('Unexpected JSON response format from n8n:', data);\n          botResponseText = \"I received an unexpected response format. Please try again.\";\n        }\n      } catch (jsonError) {\n        // If JSON parsing fails, treat the response as plain text\n        console.log('Response is not JSON, treating as plain text:', responseText);\n        botResponseText = responseText.trim();\n      }\n\n      if (botResponseText) {\n        const botResponse: Message = {\n          id: Date.now(),\n          text: botResponseText,\n          isUser: false,\n          timestamp: new Date()\n        };\n\n        setMessages(prev => [...prev, botResponse]);\n      } else {\n        const errorMessage: Message = {\n          id: Date.now(),\n          text: \"I received an empty response. Please try again.\",\n          isUser: false,\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, errorMessage]);\n      }\n    } catch (error) {\n      console.error('Error sending message to n8n:', error);\n\n      // Show error message to user\n      const errorMessage: Message = {\n        id: Date.now(),\n        text: \"Sorry, I'm having trouble connecting right now. Please try again later.\",\n        isUser: false,\n        timestamp: new Date()\n      };\n\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleSendMessage = async () => {\n    if (inputText.trim() === '' || isLoading) return;\n\n    const userMessage: Message = {\n      id: Date.now(),\n      text: inputText,\n      isUser: true,\n      timestamp: new Date()\n    };\n\n    // Add user message immediately\n    setMessages(prev => [...prev, userMessage]);\n    const messageToSend = inputText;\n    setInputText('');\n\n    // Send to n8n and wait for response\n    await sendToN8n(messageToSend);\n  };\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  return (\n    <div className=\"fixed bottom-4 right-4 z-50\">\n      {/* Chat Button */}\n      {!isOpen && (\n        <button\n          onClick={() => setIsOpen(true)}\n          className=\"bg-black text-white rounded-full w-14 h-14 flex items-center justify-center shadow-lg hover:bg-gray-800 transition-colors\"\n        >\n          <svg\n            className=\"w-6 h-6\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            viewBox=\"0 0 24 24\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n          >\n            <path\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              strokeWidth={2}\n              d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n            />\n          </svg>\n        </button>\n      )}\n\n      {/* Chat Window */}\n      {isOpen && (\n        <div className=\"bg-white border border-gray-300 rounded-lg shadow-xl w-80 h-96 flex flex-col\">\n          {/* Header */}\n          <div className=\"bg-black text-white p-4 rounded-t-lg flex justify-between items-center\">\n            <h3 className=\"font-semibold\">Chat Support</h3>\n            <button\n              onClick={() => setIsOpen(false)}\n              className=\"text-white hover:text-gray-300\"\n            >\n              <svg\n                className=\"w-5 h-5\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M6 18L18 6M6 6l12 12\"\n                />\n              </svg>\n            </button>\n          </div>\n\n          {/* Messages */}\n          <div className=\"flex-1 p-4 overflow-y-auto space-y-3\">\n            {messages.map((message) => (\n              <div\n                key={message.id}\n                className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}\n              >\n                <div\n                  className={`max-w-xs px-3 py-2 rounded-lg text-sm ${\n                    message.isUser\n                      ? 'bg-black text-white'\n                      : 'bg-gray-100 text-black border border-gray-200'\n                  }`}\n                >\n                  {message.text}\n                </div>\n              </div>\n            ))}\n\n            {/* Loading indicator */}\n            {isLoading && (\n              <div className=\"flex justify-start\">\n                <div className=\"bg-gray-100 text-black border border-gray-200 max-w-xs px-3 py-2 rounded-lg text-sm\">\n                  <div className=\"flex items-center space-x-1\">\n                    <div className=\"flex space-x-1\">\n                      <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n                      <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{animationDelay: '0.1s'}}></div>\n                      <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{animationDelay: '0.2s'}}></div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            <div ref={messagesEndRef} />\n          </div>\n\n          {/* Input */}\n          <div className=\"p-4 border-t border-gray-200\">\n            <div className=\"flex space-x-2\">\n              <input\n                type=\"text\"\n                value={inputText}\n                onChange={(e) => setInputText(e.target.value)}\n                onKeyDown={handleKeyDown}\n                placeholder=\"Type your message...\"\n                disabled={isLoading}\n                className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent text-sm disabled:opacity-50\"\n              />\n              <button\n                onClick={handleSendMessage}\n                disabled={isLoading || inputText.trim() === ''}\n                className=\"bg-black text-white px-4 py-2 rounded-lg hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {isLoading ? (\n                  <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                ) : (\n                  <svg\n                    className=\"w-4 h-4\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      strokeWidth={2}\n                      d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"\n                    />\n                  </svg>\n                )}\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAae,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,iNAAQ,EAAC;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAY;QAClD;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,WAAW,IAAI;QACjB;KACD;IACD,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAC;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAC;IAC3C,MAAM,CAAC,UAAU,GAAG,IAAA,iNAAQ,EAAC,IAAM,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK;IACzG,MAAM,iBAAiB,IAAA,+MAAM,EAAiB;IAE9C,4BAA4B;IAC5B,MAAM,kBAAkB;IACxB,MAAM,QAAQ;IAEd,iDAAiD;IACjD,IAAA,kNAAS,EAAC;QACR,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D,GAAG;QAAC;KAAS;IAEb,MAAM,YAAY,OAAO;QACvB,IAAI;YACF,aAAa;YAEb,MAAM,UAAU;gBACd;oBACE,QAAQ;oBACR,WAAW;oBACX,OAAO;oBACP,WAAW;oBACX,UAAU;wBACR,WAAW,IAAI,OAAO,WAAW;oBACnC;gBACF;aACD;YAED,QAAQ,GAAG,CAAC,mBAAmB;gBAAE,KAAK;gBAAiB;YAAQ;YAE/D,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,QAAQ,GAAG,CAAC,wBAAwB,SAAS,MAAM;YAEnD,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,uBAAuB;gBACrC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,WAAW;YACzE;YAEA,6BAA6B;YAC7B,MAAM,eAAe,MAAM,SAAS,IAAI;YACxC,QAAQ,GAAG,CAAC,qBAAqB;YAEjC,IAAI,kBAAkB;YAEtB,IAAI;gBACF,6BAA6B;gBAC7B,MAAM,OAAO,KAAK,KAAK,CAAC;gBACxB,QAAQ,GAAG,CAAC,yBAAyB;gBAErC,mCAAmC;gBACnC,IAAI,MAAM,OAAO,CAAC,SAAS,KAAK,MAAM,GAAG,KAAK,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE;oBAC5D,kBAAkB,IAAI,CAAC,EAAE,CAAC,MAAM;gBAClC,OAAO,IAAI,KAAK,MAAM,EAAE;oBACtB,8BAA8B;oBAC9B,kBAAkB,KAAK,MAAM;gBAC/B,OAAO;oBACL,QAAQ,IAAI,CAAC,6CAA6C;oBAC1D,kBAAkB;gBACpB;YACF,EAAE,OAAO,WAAW;gBAClB,0DAA0D;gBAC1D,QAAQ,GAAG,CAAC,iDAAiD;gBAC7D,kBAAkB,aAAa,IAAI;YACrC;YAEA,IAAI,iBAAiB;gBACnB,MAAM,cAAuB;oBAC3B,IAAI,KAAK,GAAG;oBACZ,MAAM;oBACN,QAAQ;oBACR,WAAW,IAAI;gBACjB;gBAEA,YAAY,CAAA,OAAQ;2BAAI;wBAAM;qBAAY;YAC5C,OAAO;gBACL,MAAM,eAAwB;oBAC5B,IAAI,KAAK,GAAG;oBACZ,MAAM;oBACN,QAAQ;oBACR,WAAW,IAAI;gBACjB;gBACA,YAAY,CAAA,OAAQ;2BAAI;wBAAM;qBAAa;YAC7C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAE/C,6BAA6B;YAC7B,MAAM,eAAwB;gBAC5B,IAAI,KAAK,GAAG;gBACZ,MAAM;gBACN,QAAQ;gBACR,WAAW,IAAI;YACjB;YAEA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAa;QAC7C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,UAAU,IAAI,OAAO,MAAM,WAAW;QAE1C,MAAM,cAAuB;YAC3B,IAAI,KAAK,GAAG;YACZ,MAAM;YACN,QAAQ;YACR,WAAW,IAAI;QACjB;QAEA,+BAA+B;QAC/B,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,MAAM,gBAAgB;QACtB,aAAa;QAEb,oCAAoC;QACpC,MAAM,UAAU;IAClB;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;YAEZ,CAAC,wBACA,8OAAC;gBACC,SAAS,IAAM,UAAU;gBACzB,WAAU;0BAEV,cAAA,8OAAC;oBACC,WAAU;oBACV,MAAK;oBACL,QAAO;oBACP,SAAQ;oBACR,OAAM;8BAEN,cAAA,8OAAC;wBACC,eAAc;wBACd,gBAAe;wBACf,aAAa;wBACb,GAAE;;;;;;;;;;;;;;;;YAOT,wBACC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAgB;;;;;;0CAC9B,8OAAC;gCACC,SAAS,IAAM,UAAU;gCACzB,WAAU;0CAEV,cAAA,8OAAC;oCACC,WAAU;oCACV,MAAK;oCACL,QAAO;oCACP,SAAQ;8CAER,cAAA,8OAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,aAAa;wCACb,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAOV,8OAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;oCAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,MAAM,GAAG,gBAAgB,iBAAiB;8CAErE,cAAA,8OAAC;wCACC,WAAW,CAAC,sCAAsC,EAChD,QAAQ,MAAM,GACV,wBACA,iDACJ;kDAED,QAAQ,IAAI;;;;;;mCAVV,QAAQ,EAAE;;;;;4BAgBlB,2BACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;oDAAkD,OAAO;wDAAC,gBAAgB;oDAAM;;;;;;8DAC/F,8OAAC;oDAAI,WAAU;oDAAkD,OAAO;wDAAC,gBAAgB;oDAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOzG,8OAAC;gCAAI,KAAK;;;;;;;;;;;;kCAIZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oCAC5C,WAAW;oCACX,aAAY;oCACZ,UAAU;oCACV,WAAU;;;;;;8CAEZ,8OAAC;oCACC,SAAS;oCACT,UAAU,aAAa,UAAU,IAAI,OAAO;oCAC5C,WAAU;8CAET,0BACC,8OAAC;wCAAI,WAAU;;;;;6DAEf,8OAAC;wCACC,WAAU;wCACV,MAAK;wCACL,QAAO;wCACP,SAAQ;kDAER,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWxB", "debugId": null}}, {"offset": {"line": 411, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Rohan/CW%20manual%20edit%20from%20github/chat-widget-app/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 430, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Rohan/CW%20manual%20edit%20from%20github/chat-widget-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 435, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/R<PERSON>an/CW%20manual%20edit%20from%20github/chat-widget-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}