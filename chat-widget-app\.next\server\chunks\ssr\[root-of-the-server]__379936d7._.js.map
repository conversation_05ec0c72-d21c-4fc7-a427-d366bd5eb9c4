{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/R<PERSON>an/CW%20manual%20edit%20from%20github/chat-widget-app/src/components/ChatWidget.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface Message {\n  id: number;\n  text: string;\n  isUser: boolean;\n  timestamp: Date;\n}\n\nexport default function ChatWidget() {\n  const [isOpen, setIsOpen] = useState(false);\n  const [messages, setMessages] = useState<Message[]>([\n    {\n      id: 1,\n      text: \"Hello! How can I help you today?\",\n      isUser: false,\n      timestamp: new Date()\n    }\n  ]);\n  const [inputText, setInputText] = useState('');\n\n  const handleSendMessage = () => {\n    if (inputText.trim() === '') return;\n\n    const newMessage: Message = {\n      id: messages.length + 1,\n      text: inputText,\n      isUser: true,\n      timestamp: new Date()\n    };\n\n    setMessages([...messages, newMessage]);\n    setInputText('');\n\n    // Simulate bot response\n    setTimeout(() => {\n      const botResponse: Message = {\n        id: messages.length + 2,\n        text: \"Thanks for your message! This is a demo response. In a real implementation, this would connect to your chatbot backend.\",\n        isUser: false,\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, botResponse]);\n    }, 1000);\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter') {\n      handleSendMessage();\n    }\n  };\n\n  return (\n    <div className=\"fixed bottom-4 right-4 z-50\">\n      {/* Chat Button */}\n      {!isOpen && (\n        <button\n          onClick={() => setIsOpen(true)}\n          className=\"bg-black text-white rounded-full w-14 h-14 flex items-center justify-center shadow-lg hover:bg-gray-800 transition-colors\"\n        >\n          <svg\n            className=\"w-6 h-6\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            viewBox=\"0 0 24 24\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n          >\n            <path\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              strokeWidth={2}\n              d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n            />\n          </svg>\n        </button>\n      )}\n\n      {/* Chat Window */}\n      {isOpen && (\n        <div className=\"bg-white border border-gray-300 rounded-lg shadow-xl w-80 h-96 flex flex-col\">\n          {/* Header */}\n          <div className=\"bg-black text-white p-4 rounded-t-lg flex justify-between items-center\">\n            <h3 className=\"font-semibold\">Chat Support</h3>\n            <button\n              onClick={() => setIsOpen(false)}\n              className=\"text-white hover:text-gray-300\"\n            >\n              <svg\n                className=\"w-5 h-5\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M6 18L18 6M6 6l12 12\"\n                />\n              </svg>\n            </button>\n          </div>\n\n          {/* Messages */}\n          <div className=\"flex-1 p-4 overflow-y-auto space-y-3\">\n            {messages.map((message) => (\n              <div\n                key={message.id}\n                className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}\n              >\n                <div\n                  className={`max-w-xs px-3 py-2 rounded-lg text-sm ${\n                    message.isUser\n                      ? 'bg-black text-white'\n                      : 'bg-gray-100 text-black border border-gray-200'\n                  }`}\n                >\n                  {message.text}\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* Input */}\n          <div className=\"p-4 border-t border-gray-200\">\n            <div className=\"flex space-x-2\">\n              <input\n                type=\"text\"\n                value={inputText}\n                onChange={(e) => setInputText(e.target.value)}\n                onKeyPress={handleKeyPress}\n                placeholder=\"Type your message...\"\n                className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent text-sm\"\n              />\n              <button\n                onClick={handleSendMessage}\n                className=\"bg-black text-white px-4 py-2 rounded-lg hover:bg-gray-800 transition-colors\"\n              >\n                <svg\n                  className=\"w-4 h-4\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"\n                  />\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAWe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,iNAAQ,EAAC;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAY;QAClD;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,WAAW,IAAI;QACjB;KACD;IACD,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAC;IAE3C,MAAM,oBAAoB;QACxB,IAAI,UAAU,IAAI,OAAO,IAAI;QAE7B,MAAM,aAAsB;YAC1B,IAAI,SAAS,MAAM,GAAG;YACtB,MAAM;YACN,QAAQ;YACR,WAAW,IAAI;QACjB;QAEA,YAAY;eAAI;YAAU;SAAW;QACrC,aAAa;QAEb,wBAAwB;QACxB,WAAW;YACT,MAAM,cAAuB;gBAC3B,IAAI,SAAS,MAAM,GAAG;gBACtB,MAAM;gBACN,QAAQ;gBACR,WAAW,IAAI;YACjB;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAY;QAC5C,GAAG;IACL;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;YAEZ,CAAC,wBACA,8OAAC;gBACC,SAAS,IAAM,UAAU;gBACzB,WAAU;0BAEV,cAAA,8OAAC;oBACC,WAAU;oBACV,MAAK;oBACL,QAAO;oBACP,SAAQ;oBACR,OAAM;8BAEN,cAAA,8OAAC;wBACC,eAAc;wBACd,gBAAe;wBACf,aAAa;wBACb,GAAE;;;;;;;;;;;;;;;;YAOT,wBACC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAgB;;;;;;0CAC9B,8OAAC;gCACC,SAAS,IAAM,UAAU;gCACzB,WAAU;0CAEV,cAAA,8OAAC;oCACC,WAAU;oCACV,MAAK;oCACL,QAAO;oCACP,SAAQ;8CAER,cAAA,8OAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,aAAa;wCACb,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAOV,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;gCAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,MAAM,GAAG,gBAAgB,iBAAiB;0CAErE,cAAA,8OAAC;oCACC,WAAW,CAAC,sCAAsC,EAChD,QAAQ,MAAM,GACV,wBACA,iDACJ;8CAED,QAAQ,IAAI;;;;;;+BAVV,QAAQ,EAAE;;;;;;;;;;kCAiBrB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oCAC5C,YAAY;oCACZ,aAAY;oCACZ,WAAU;;;;;;8CAEZ,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC;wCACC,WAAU;wCACV,MAAK;wCACL,QAAO;wCACP,SAAQ;kDAER,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUtB", "debugId": null}}, {"offset": {"line": 237, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Rohan/CW%20manual%20edit%20from%20github/chat-widget-app/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 256, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Rohan/CW%20manual%20edit%20from%20github/chat-widget-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 261, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/R<PERSON>an/CW%20manual%20edit%20from%20github/chat-widget-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}