Include it in your HTML or your frontend page where the chat should appear
In your HTML or template, you’ll do something like:

    <script>
    window.ChatWidgetConfig = {
        webhook: {
        url: 'https://your-n8n-instance.com/webhook/YOUR_WEBHOOK_ID',
        route: 'your-route-name'
        },
        branding: {
        logo: 'YOUR_LOGO_URL',
        name: 'Your Company',
        welcomeText: 'Hi, how can I help you?',
        responseTimeText: 'We typically respond right away'
        },
        style: {
        primaryColor: '#854fff',
        secondaryColor: '#6b3fd4',
        position: 'right',
        backgroundColor: '#ffffff',
        fontColor: '#333333'
        }
    };
    </script>
    <script src="/path/to/chat-widget-v4.js"></script>





How to connect with n8n webhook (frontend → n8n) & handle AI response (n8n → frontend)

Here’s the flow:

1. Frontend (widget) → POST to n8n webhook

When the widget loads, it may send a loadPreviousSession action.

When the user types a message and sends, the widget sends a POST request with <PERSON><PERSON><PERSON> like:

[
  {
    "action": "sendMessage",
    "sessionId": "some-uuid",
    "route": "general",
    "chatInput": "Hello, how are you?",
    "metadata": {
      "userId": "..."  // optional extra metadata
    }
  }
]


Or if it's trying to load session:

[
  {
    "action": "loadPreviousSession",
    "sessionId": "some-uuid",
    "route": "general",
    "metadata": { ... }
  }
]


(This is taken from the spec in README) 
GitHub

The url for the webhook is in your ChatWidgetConfig.webhook.url.

CORS: Ensure your n8n endpoint allows CORS from your frontend origin so the browser can send requests.

2. n8n workflow: receive, process, respond

In n8n, create a Webhook node (or HTTP Endpoint) that matches the URL your widget uses.

When the webhook receives the POST, it will get JSON payload(s).

In your workflow, you parse that, look at the action field, and branch:

If action = "loadPreviousSession", you might fetch chat history (if you stored previous messages somewhere) and return them.

If action = "sendMessage", you take the chatInput, maybe send it to an AI model (OpenAI, HuggingFace, local model) or logic in your workflow, generate a response.

Then you return a response JSON to the widget.

The response must follow the format expected by the widget:

Single Response Format:

{
  "output": "Your bot's response message"
}


Or Array Format:

[
  {
    "output": "Your bot's response message"
  }
]


The widget code will look for output in the JSON and render it in the chat UI. 
GitHub

If you want more complex responses (like multiple messages, or attachments), you might need to modify the widget’s code to support them (if it doesn’t already).

3. Frontend receives & displays responses

After the widget sends the request, it waits for the response JSON from n8n.

Once it receives the response, the widget will parse it, read output (or iterate through array of outputs), and append those as messages in the chat UI (from the “bot” side).

Internally, the widget code manages mapping of sessionId and rendering.

You don’t need to do manual fetch calls—you let the widget code handle sending and receiving, as long as the webhook behaves as expected.