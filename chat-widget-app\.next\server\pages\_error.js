var R=require("../chunks/ssr/[turbopack]_runtime.js")("server/pages/_error.js")
R.c("server/chunks/ssr/4223e_92d773dc._.js")
R.c("server/chunks/ssr/[root-of-the-server]__e6a4d965._.js")
R.c("server/chunks/ssr/4223e_next_b0a95a6e._.js")
R.c("server/chunks/ssr/4223e_793be110._.js")
R.c("server/chunks/ssr/[externals]_next_dist_shared_lib_no-fallback-error_external_59b92b38.js")
R.m("[project]/chat-widget-app/node_modules/next/dist/esm/build/templates/pages.js { INNER_PAGE => \"[project]/chat-widget-app/node_modules/next/error.js [ssr] (ecmascript)\", INNER_DOCUMENT => \"[project]/chat-widget-app/node_modules/next/document.js [ssr] (ecmascript)\", INNER_APP => \"[project]/chat-widget-app/node_modules/next/app.js [ssr] (ecmascript)\" } [ssr] (ecmascript)")
module.exports=R.m("[project]/chat-widget-app/node_modules/next/dist/esm/build/templates/pages.js { INNER_PAGE => \"[project]/chat-widget-app/node_modules/next/error.js [ssr] (ecmascript)\", INNER_DOCUMENT => \"[project]/chat-widget-app/node_modules/next/document.js [ssr] (ecmascript)\", INNER_APP => \"[project]/chat-widget-app/node_modules/next/app.js [ssr] (ecmascript)\" } [ssr] (ecmascript)").exports
