<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ray White AI Chat Widget</title>
    <style>
        /* Ray White AI Chat Widget Styles */
        .ray-white-chat-widget {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 9999;
        }

        .chat-toggle {
            background: #1e3a8a;
            color: white;
            border: none;
            border-radius: 50%;
            width: 64px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 10px 25px rgba(30, 58, 138, 0.3);
            transition: all 0.3s ease;
        }

        .chat-toggle:hover {
            background: #1e40af;
            transform: scale(1.05);
        }

        .chat-window {
            position: absolute;
            bottom: 80px;
            right: 0;
            width: 384px;
            height: 500px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
            display: none;
            flex-direction: column;
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }

        .chat-window.open {
            display: flex;
        }

        .chat-header {
            background: #1e3a8a;
            color: white;
            padding: 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .chat-header-content {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .chat-logo {
            width: 32px;
            height: 32px;
            background: white;
            border-radius: 4px;
            padding: 4px;
        }

        .chat-title {
            font-size: 18px;
            font-weight: 600;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: background 0.2s;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .chat-messages {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
            background: #f9fafb;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .message {
            display: flex;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message.bot {
            justify-content: flex-start;
        }

        .message-bubble {
            max-width: 280px;
            padding: 12px 16px;
            border-radius: 16px;
            font-size: 14px;
            line-height: 1.4;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .message.user .message-bubble {
            background: #1e3a8a;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.bot .message-bubble {
            background: white;
            color: #374151;
            border: 1px solid #e5e7eb;
            border-bottom-left-radius: 4px;
        }

        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: #6b7280;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: #1e3a8a;
            border-radius: 50%;
            animation: bounce 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes bounce {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        .chat-input {
            padding: 16px;
            border-top: 1px solid #e5e7eb;
            background: white;
        }

        .input-container {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .message-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 24px;
            background: #f9fafb;
            font-size: 14px;
            outline: none;
            transition: all 0.2s;
        }

        .message-input:focus {
            border-color: #1e3a8a;
            background: white;
            box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
        }

        .send-btn {
            background: #1e3a8a;
            color: white;
            border: none;
            border-radius: 50%;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            box-shadow: 0 2px 8px rgba(30, 58, 138, 0.3);
        }

        .send-btn:hover:not(:disabled) {
            background: #1e40af;
            transform: scale(1.05);
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .session-info {
            margin-top: 8px;
            text-align: center;
            font-size: 10px;
            color: #9ca3af;
        }

        /* Responsive */
        @media (max-width: 480px) {
            .chat-window {
                width: calc(100vw - 40px);
                height: calc(100vh - 120px);
                bottom: 80px;
                right: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Ray White AI Chat Widget -->
    <div id="ray-white-chat-widget" class="ray-white-chat-widget">
        <!-- Chat Toggle Button -->
        <button class="chat-toggle" onclick="toggleChat()" title="Chat with Ray White AI">
            <svg width="28" height="28" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
            </svg>
        </button>

        <!-- Chat Window -->
        <div id="chat-window" class="chat-window">
            <!-- Header -->
            <div class="chat-header">
                <div class="chat-header-content">
                    <img src="https://raywhiteltd.com/_next/static/media/logo.60ee6487.svg" 
                         alt="Ray White Logo" class="chat-logo">
                    <div class="chat-title">Ray White AI</div>
                </div>
                <button class="close-btn" onclick="closeChat()" title="Close chat">
                    <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>

            <!-- Messages -->
            <div id="chat-messages" class="chat-messages">
                <div class="message bot">
                    <div class="message-bubble">
                        Hello! I'm Ray White AI assistant. How can I help you with your property needs today?
                    </div>
                </div>
            </div>

            <!-- Input -->
            <div class="chat-input">
                <div class="input-container">
                    <input type="text" id="message-input" class="message-input" 
                           placeholder="Ask about properties, locations, or services..." 
                           onkeydown="handleKeyDown(event)">
                    <button id="send-btn" class="send-btn" onclick="sendMessage()" title="Send message">
                        <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                        </svg>
                    </button>
                </div>
                <div class="session-info" id="session-info"></div>
            </div>
        </div>
    </div>

    <script>
        // Ray White AI Chat Widget JavaScript
        class RayWhiteChatWidget {
            constructor() {
                this.isOpen = false;
                this.isLoading = false;
                this.session = this.createSession();
                this.n8nWebhookUrl = 'https://raywhiteltd.app.n8n.cloud/webhook/84fc3ca5-f11a-41f5-b520-c5a22c8c7a05/chat';
                this.route = 'general';
                
                this.initializeSessionInfo();
            }

            createSession() {
                const userId = `user-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
                const sessionId = `session-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
                return {
                    sessionId,
                    userId,
                    startTime: new Date(),
                    lastActivity: new Date()
                };
            }

            initializeSessionInfo() {
                const sessionInfo = document.getElementById('session-info');
                sessionInfo.textContent = `Session: ${this.session.sessionId.split('-')[1]}`;
            }

            toggleChat() {
                this.isOpen = !this.isOpen;
                const chatWindow = document.getElementById('chat-window');
                chatWindow.classList.toggle('open', this.isOpen);
                
                if (this.isOpen) {
                    document.getElementById('message-input').focus();
                }
            }

            closeChat() {
                this.isOpen = false;
                document.getElementById('chat-window').classList.remove('open');
            }

            addMessage(text, isUser = false) {
                const messagesContainer = document.getElementById('chat-messages');
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${isUser ? 'user' : 'bot'}`;
                
                const bubbleDiv = document.createElement('div');
                bubbleDiv.className = 'message-bubble';
                bubbleDiv.textContent = text;
                
                messageDiv.appendChild(bubbleDiv);
                messagesContainer.appendChild(messageDiv);
                
                // Scroll to bottom
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }

            showTypingIndicator() {
                const messagesContainer = document.getElementById('chat-messages');
                const typingDiv = document.createElement('div');
                typingDiv.id = 'typing-indicator';
                typingDiv.className = 'message bot';
                typingDiv.innerHTML = `
                    <div class="message-bubble">
                        <div class="typing-indicator">
                            <span>Ray White AI is typing</span>
                            <div class="typing-dots">
                                <div class="typing-dot"></div>
                                <div class="typing-dot"></div>
                                <div class="typing-dot"></div>
                            </div>
                        </div>
                    </div>
                `;
                messagesContainer.appendChild(typingDiv);
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }

            hideTypingIndicator() {
                const typingIndicator = document.getElementById('typing-indicator');
                if (typingIndicator) {
                    typingIndicator.remove();
                }
            }

            async sendToN8n(message) {
                try {
                    this.isLoading = true;
                    this.updateSendButton();
                    this.showTypingIndicator();
                    
                    const payload = [{
                        action: "sendMessage",
                        sessionId: this.session.sessionId,
                        route: this.route,
                        chatInput: message,
                        metadata: {
                            timestamp: new Date().toISOString(),
                            userId: this.session.userId,
                            sessionStartTime: this.session.startTime.toISOString()
                        }
                    }];

                    console.log('Sending to n8n:', { url: this.n8nWebhookUrl, payload });

                    const response = await fetch(this.n8nWebhookUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(payload)
                    });

                    console.log('n8n response status:', response.status);

                    if (!response.ok) {
                        const errorText = await response.text();
                        console.error('n8n error response:', errorText);
                        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
                    }

                    const responseText = await response.text();
                    console.log('n8n raw response:', responseText);

                    let botResponseText = '';

                    try {
                        const data = JSON.parse(responseText);
                        console.log('n8n parsed JSON data:', data);
                        
                        if (Array.isArray(data) && data.length > 0 && data[0].output) {
                            botResponseText = data[0].output;
                        } else if (data.output) {
                            botResponseText = data.output;
                        } else {
                            console.warn('Unexpected JSON response format from n8n:', data);
                            botResponseText = "I received an unexpected response format. Please try again.";
                        }
                    } catch (jsonError) {
                        console.log('Response is not JSON, treating as plain text:', responseText);
                        botResponseText = responseText.trim();
                    }

                    this.hideTypingIndicator();

                    if (botResponseText) {
                        this.addMessage(botResponseText, false);
                    } else {
                        this.addMessage("I received an empty response. Please try again.", false);
                    }

                } catch (error) {
                    console.error('Error sending message to n8n:', error);
                    this.hideTypingIndicator();
                    this.addMessage("Sorry, I'm having trouble connecting right now. Please try again later.", false);
                } finally {
                    this.isLoading = false;
                    this.updateSendButton();
                    this.session.lastActivity = new Date();
                }
            }

            updateSendButton() {
                const sendBtn = document.getElementById('send-btn');
                const messageInput = document.getElementById('message-input');
                
                sendBtn.disabled = this.isLoading || messageInput.value.trim() === '';
                messageInput.disabled = this.isLoading;
                
                if (this.isLoading) {
                    sendBtn.innerHTML = `
                        <div style="width: 20px; height: 20px; border: 2px solid white; border-top: 2px solid transparent; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                        <style>
                            @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
                        </style>
                    `;
                } else {
                    sendBtn.innerHTML = `
                        <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                        </svg>
                    `;
                }
            }

            async sendMessage() {
                const messageInput = document.getElementById('message-input');
                const message = messageInput.value.trim();
                
                if (message === '' || this.isLoading) return;
                
                // Add user message
                this.addMessage(message, true);
                messageInput.value = '';
                this.updateSendButton();
                
                // Send to n8n
                await this.sendToN8n(message);
            }

            handleKeyDown(event) {
                if (event.key === 'Enter' && !event.shiftKey) {
                    event.preventDefault();
                    this.sendMessage();
                }
            }
        }

        // Initialize the chat widget
        const chatWidget = new RayWhiteChatWidget();

        // Global functions for HTML onclick events
        function toggleChat() {
            chatWidget.toggleChat();
        }

        function closeChat() {
            chatWidget.closeChat();
        }

        function sendMessage() {
            chatWidget.sendMessage();
        }

        function handleKeyDown(event) {
            chatWidget.handleKeyDown(event);
        }

        // Update send button state on input change
        document.getElementById('message-input').addEventListener('input', function() {
            chatWidget.updateSendButton();
        });
    </script>
</body>
</html>
