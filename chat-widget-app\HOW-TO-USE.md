# Ray White AI Chat Widget - How to Use

## Overview
The Ray White AI Chat Widget is a modern, responsive chat interface that connects to your n8n webhook backend. It provides a seamless way for website visitors to interact with your AI assistant.

## Features
- ✅ **Modern Design**: Dark navy blue theme matching Ray White branding
- ✅ **Responsive**: Works on desktop, tablet, and mobile devices
- ✅ **Real-time Communication**: Connects to n8n webhook for AI responses
- ✅ **Session Tracking**: Unique session IDs for conversation tracking
- ✅ **Loading States**: Visual feedback during message processing
- ✅ **Error Handling**: Graceful handling of connection issues
- ✅ **Easy Integration**: Drop-in solution for any website

## Installation Methods

### Method 1: Standalone HTML File (Recommended for Testing)
1. Download `standalone-chat-widget.html`
2. Open it in any web browser
3. The chat widget will be fully functional

### Method 2: JavaScript Integration (Recommended for Production)
1. Download `ray-white-chat-widget.js`
2. Include it in your website:
```html
<script src="ray-white-chat-widget.js"></script>
```
3. The widget will automatically initialize when the page loads

### Method 3: React Component (For Next.js/React Projects)
1. Copy the `ChatWidget.tsx` component
2. Import and use in your React application:
```jsx
import ChatWidget from './components/ChatWidget';

function App() {
  return (
    <div>
      {/* Your page content */}
      <ChatWidget />
    </div>
  );
}
```

## Configuration

### JavaScript Widget Configuration
You can customize the widget by setting configuration before loading the script:

```html
<script>
window.RayWhiteChatConfig = {
  webhookUrl: 'https://your-custom-n8n-webhook-url',
  route: 'your-custom-route',
  position: 'bottom-right' // or 'bottom-left'
};
</script>
<script src="ray-white-chat-widget.js"></script>
```

### Default Configuration
- **Webhook URL**: `https://raywhiteltd.app.n8n.cloud/webhook/84fc3ca5-f11a-41f5-b520-c5a22c8c7a05/chat`
- **Route**: `general`
- **Position**: `bottom-right`

## N8n Integration

### Expected Request Format
The widget sends requests to your n8n webhook in this format:
```json
[
  {
    "action": "sendMessage",
    "sessionId": "session-1234567890-abc123",
    "route": "general",
    "chatInput": "User's message here",
    "metadata": {
      "timestamp": "2024-01-01T12:00:00.000Z",
      "userId": "user-1234567890-xyz789",
      "sessionStartTime": "2024-01-01T11:55:00.000Z"
    }
  }
]
```

### Expected Response Formats
The widget handles multiple response formats:

**JSON Array Format (Recommended):**
```json
[
  {
    "output": "Hello! How can I help you today?"
  }
]
```

**JSON Object Format:**
```json
{
  "output": "Hello! How can I help you today?"
}
```

**Plain Text Format:**
```
Hello! How can I help you today?
```

## User Interface

### Chat Button
- **Location**: Bottom-right corner (configurable)
- **Design**: Dark navy blue circular button with chat icon
- **Hover Effect**: Slight scale animation
- **Tooltip**: "Chat with Ray White AI"

### Chat Window
- **Size**: 384px × 500px (responsive on mobile)
- **Header**: Ray White logo + "Ray White AI" title
- **Messages**: Scrollable area with user/bot message bubbles
- **Input**: Rounded input field with send button
- **Session Info**: Shows abbreviated session ID

### Message Styling
- **User Messages**: Dark navy blue bubbles, right-aligned
- **Bot Messages**: White bubbles with border, left-aligned
- **Typing Indicator**: Animated dots with "Ray White AI is typing"
- **Loading States**: Spinner in send button, disabled input

## Browser Compatibility
- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ Mobile browsers (iOS Safari, Chrome Mobile)

## Performance
- **Bundle Size**: ~15KB (JavaScript + CSS)
- **Dependencies**: None (vanilla JavaScript)
- **Load Time**: Instant initialization
- **Memory Usage**: Minimal footprint

## Troubleshooting

### Common Issues

**1. Widget Not Appearing**
- Check if the script is loaded correctly
- Verify no JavaScript errors in browser console
- Ensure the script runs after DOM is ready

**2. Messages Not Sending**
- Check network tab for failed requests
- Verify n8n webhook URL is correct and accessible
- Check CORS settings on your n8n instance

**3. No Response from AI**
- Verify n8n workflow is active and working
- Check n8n logs for errors
- Test webhook directly with curl/Postman

**4. Styling Issues**
- Check for CSS conflicts with existing styles
- Verify the widget's z-index (9999) is high enough
- Test on different screen sizes

### Debug Mode
Enable console logging by opening browser developer tools (F12). The widget logs all requests and responses for debugging.

## Security Considerations
- The widget only sends user messages and metadata to the configured webhook
- Session IDs are generated client-side and are not personally identifiable
- No sensitive data is stored in browser localStorage
- All communication uses HTTPS

## Customization

### Styling
To customize the appearance, you can override CSS classes:
```css
.ray-white-chat-widget {
  /* Custom positioning */
}

.ray-white-chat-toggle {
  /* Custom button styling */
}

.ray-white-chat-window {
  /* Custom window styling */
}
```

### Behavior
For advanced customization, modify the JavaScript widget class or create a custom implementation based on the provided code.

## Support
For technical support or customization requests, please contact the development team with:
- Browser version and operating system
- Console error messages (if any)
- Steps to reproduce the issue
- Expected vs actual behavior
