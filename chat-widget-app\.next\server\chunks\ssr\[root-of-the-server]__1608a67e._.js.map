{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/R<PERSON>an/CW%20manual%20edit%20from%20github/chat-widget-app/src/components/ChatWidget.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ChatWidget.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ChatWidget.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAiS,GAC9T,+DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/R<PERSON>an/CW%20manual%20edit%20from%20github/chat-widget-app/src/components/ChatWidget.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ChatWidget.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ChatWidget.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA6Q,GAC1S,2CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/R<PERSON>an/CW%20manual%20edit%20from%20github/chat-widget-app/src/app/page.tsx"], "sourcesContent": ["import ChatWidget from '@/components/ChatWidget';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-white text-black\">\n      {/* Main Content */}\n      <div className=\"flex flex-col items-center justify-center min-h-screen p-8\">\n        <h1 className=\"text-4xl font-bold mb-8 text-center\">\n          Welcome to Our Website\n        </h1>\n        <p className=\"text-lg text-gray-700 text-center max-w-2xl mb-8\">\n          This is a simple black and white page with a chat widget in the corner.\n          You can interact with the chat widget to get assistance or ask questions.\n        </p>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl\">\n          <div className=\"border border-gray-300 p-6 rounded-lg\">\n            <h2 className=\"text-xl font-semibold mb-4\">Feature 1</h2>\n            <p className=\"text-gray-600\">\n              Lorem ipsum dolor sit amet, consectetur adipiscing elit.\n              Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\n            </p>\n          </div>\n          <div className=\"border border-gray-300 p-6 rounded-lg\">\n            <h2 className=\"text-xl font-semibold mb-4\">Feature 2</h2>\n            <p className=\"text-gray-600\">\n              Ut enim ad minim veniam, quis nostrud exercitation ullamco\n              laboris nisi ut aliquip ex ea commodo consequat.\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Chat Widget */}\n      <ChatWidget />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAGpD,8OAAC;wBAAE,WAAU;kCAAmD;;;;;;kCAIhE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAK/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;0BASnC,8OAAC,2IAAU;;;;;;;;;;;AAGjB", "debugId": null}}]}