# Ray White Chat Widget - Deployment Checklist

## 📋 Pre-Deployment Checklist

### ✅ Files to Copy
- [ ] `components/ChatWidget.tsx` - Main chat widget component
- [ ] `package.json` - Dependencies reference
- [ ] `tsconfig.json` - TypeScript configuration
- [ ] `next-env.d.ts` - Next.js type definitions

### ✅ Project Requirements
- [ ] Next.js 15+ installed
- [ ] React 18+ installed
- [ ] TypeScript 5+ configured
- [ ] Tailwind CSS configured and working

### ✅ Integration Steps
1. [ ] Copy `ChatWidget.tsx` to your `src/components/` directory
2. [ ] Import the component in your desired page/layout
3. [ ] Test the chat functionality
4. [ ] Verify responsive design on mobile
5. [ ] Check browser compatibility

## 🎯 Configuration Verification

### ✅ Webhook Configuration
- [ ] n8n webhook URL is correct: `https://raywhiteltd.app.n8n.cloud/webhook/84fc3ca5-f11a-41f5-b520-c5a22c8c7a05/chat`
- [ ] Route is set to: `general`
- [ ] CORS is configured on n8n for your domain

### ✅ Visual Design
- [ ] Color scheme is Ray White branded (#1A2B3C)
- [ ] Logo displays correctly from: `https://raywhiteltd.com/_next/static/media/logo.60ee6487.svg`
- [ ] Send button points in the right direction (chevron right)
- [ ] Widget appears in bottom-right corner
- [ ] Responsive design works on mobile devices

### ✅ Functionality Testing
- [ ] Chat button opens the widget
- [ ] Messages can be sent and received
- [ ] Loading states display correctly
- [ ] Error handling works when n8n is unavailable
- [ ] Session tracking is working (check console logs)
- [ ] Auto-scroll to new messages works

## 🚀 Deployment Steps

### Step 1: Environment Setup
```bash
# Ensure dependencies are installed
npm install next@^15.0.0 react@^18.0.0 react-dom@^18.0.0
npm install -D typescript@^5.0.0 @types/react@^18.0.0 @types/node@^20.0.0
```

### Step 2: Copy Component
```bash
# Copy the ChatWidget component
cp ray-white-nextjs-chat-widget/components/ChatWidget.tsx src/components/
```

### Step 3: Integration
Choose your integration method:

**Global Integration (Recommended):**
```tsx
// src/app/layout.tsx (App Router)
import ChatWidget from '@/components/ChatWidget';

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        {children}
        <ChatWidget />
      </body>
    </html>
  );
}
```

**Page-specific Integration:**
```tsx
// src/app/page.tsx
import ChatWidget from '@/components/ChatWidget';

export default function HomePage() {
  return (
    <div>
      {/* Your page content */}
      <ChatWidget />
    </div>
  );
}
```

### Step 4: Testing
```bash
# Start development server
npm run dev

# Test in browser
# 1. Open http://localhost:3000
# 2. Look for chat button in bottom-right
# 3. Test sending messages
# 4. Verify responses from n8n
```

### Step 5: Production Build
```bash
# Build for production
npm run build

# Test production build locally
npm run start
```

## 🔧 Troubleshooting

### Common Issues & Solutions

**Issue: Widget not appearing**
- ✅ Check if Tailwind CSS is properly configured
- ✅ Verify component import path is correct
- ✅ Check browser console for JavaScript errors

**Issue: Styling looks broken**
- ✅ Ensure Tailwind CSS is loaded
- ✅ Check for CSS conflicts with existing styles
- ✅ Verify all Tailwind classes are available

**Issue: Messages not sending**
- ✅ Check network tab for failed requests
- ✅ Verify n8n webhook URL is accessible
- ✅ Ensure CORS is configured on n8n for your domain

**Issue: TypeScript errors**
- ✅ Ensure TypeScript is properly configured
- ✅ Check if all type definitions are installed
- ✅ Verify React types are up to date

## 📊 Performance Monitoring

### Metrics to Monitor
- [ ] Chat widget load time
- [ ] Message send/receive latency
- [ ] n8n webhook response times
- [ ] Error rates and types
- [ ] User engagement metrics

### Monitoring Tools
- Browser Developer Tools (Network tab)
- Next.js built-in analytics
- n8n workflow logs
- Custom error tracking

## 🔒 Security Checklist

### Data Protection
- [ ] All communication uses HTTPS
- [ ] No sensitive data stored in browser
- [ ] Session IDs are non-personally identifiable
- [ ] Input sanitization handled by React

### CORS Configuration
- [ ] n8n webhook allows your production domain
- [ ] Proper CORS headers configured
- [ ] No wildcard origins in production

## 📱 Browser Testing

### Desktop Browsers
- [ ] Chrome 60+
- [ ] Firefox 55+
- [ ] Safari 12+
- [ ] Edge 79+

### Mobile Browsers
- [ ] iOS Safari
- [ ] Chrome Mobile
- [ ] Samsung Internet
- [ ] Firefox Mobile

### Responsive Testing
- [ ] Desktop (1920x1080)
- [ ] Tablet (768x1024)
- [ ] Mobile (375x667)
- [ ] Large Mobile (414x896)

## 🚀 Go-Live Checklist

### Final Verification
- [ ] All tests pass in production environment
- [ ] n8n webhook responds correctly from production
- [ ] Chat widget appears and functions properly
- [ ] No console errors or warnings
- [ ] Performance is acceptable
- [ ] Mobile experience is smooth

### Post-Deployment
- [ ] Monitor error logs for first 24 hours
- [ ] Check n8n webhook logs for any issues
- [ ] Verify user feedback is positive
- [ ] Document any issues for future reference

## 📞 Support Contacts

### Technical Issues
- Development Team: [Your team contact]
- n8n Webhook Issues: [n8n admin contact]
- Infrastructure: [DevOps contact]

### Emergency Contacts
- Critical Issues: [Emergency contact]
- After Hours: [On-call contact]

---

**Deployment Date**: ___________  
**Deployed By**: ___________  
**Verified By**: ___________  
**Version**: 1.0.0
