/**
 * <PERSON> White AI Chat Widget
 * Standalone JavaScript widget for embedding in any website
 * 
 * Usage:
 * 1. Include this script in your HTML: <script src="ray-white-chat-widget.js"></script>
 * 2. The widget will automatically initialize when the page loads
 * 
 * Configuration (optional):
 * window.RayWhiteChatConfig = {
 *   webhookUrl: 'your-custom-webhook-url',
 *   route: 'your-custom-route',
 *   position: 'bottom-right' // or 'bottom-left'
 * };
 */

(function() {
    'use strict';

    // Configuration
    const config = window.RayWhiteChatConfig || {};
    const WEBHOOK_URL = config.webhookUrl || 'https://raywhiteltd.app.n8n.cloud/webhook/84fc3ca5-f11a-41f5-b520-c5a22c8c7a05/chat';
    const ROUTE = config.route || 'general';
    const POSITION = config.position || 'bottom-right';

    // Inject CSS styles
    const styles = `
        .ray-white-chat-widget {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            position: fixed;
            ${POSITION.includes('right') ? 'right: 20px;' : 'left: 20px;'}
            bottom: 20px;
            z-index: 9999;
        }

        .ray-white-chat-toggle {
            background: #1e3a8a;
            color: white;
            border: none;
            border-radius: 50%;
            width: 64px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 10px 25px rgba(30, 58, 138, 0.3);
            transition: all 0.3s ease;
        }

        .ray-white-chat-toggle:hover {
            background: #1e40af;
            transform: scale(1.05);
        }

        .ray-white-chat-window {
            position: absolute;
            bottom: 80px;
            ${POSITION.includes('right') ? 'right: 0;' : 'left: 0;'}
            width: 384px;
            height: 500px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
            display: none;
            flex-direction: column;
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }

        .ray-white-chat-window.open {
            display: flex;
        }

        .ray-white-chat-header {
            background: #1e3a8a;
            color: white;
            padding: 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .ray-white-chat-header-content {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .ray-white-chat-logo {
            width: 32px;
            height: 32px;
            background: white;
            border-radius: 4px;
            padding: 4px;
        }

        .ray-white-chat-title {
            font-size: 18px;
            font-weight: 600;
        }

        .ray-white-close-btn {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: background 0.2s;
        }

        .ray-white-close-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .ray-white-chat-messages {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
            background: #f9fafb;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .ray-white-message {
            display: flex;
        }

        .ray-white-message.user {
            justify-content: flex-end;
        }

        .ray-white-message.bot {
            justify-content: flex-start;
        }

        .ray-white-message-bubble {
            max-width: 280px;
            padding: 12px 16px;
            border-radius: 16px;
            font-size: 14px;
            line-height: 1.4;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .ray-white-message.user .ray-white-message-bubble {
            background: #1e3a8a;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .ray-white-message.bot .ray-white-message-bubble {
            background: white;
            color: #374151;
            border: 1px solid #e5e7eb;
            border-bottom-left-radius: 4px;
        }

        .ray-white-typing-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: #6b7280;
        }

        .ray-white-typing-dots {
            display: flex;
            gap: 4px;
        }

        .ray-white-typing-dot {
            width: 8px;
            height: 8px;
            background: #1e3a8a;
            border-radius: 50%;
            animation: ray-white-bounce 1.4s infinite ease-in-out;
        }

        .ray-white-typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .ray-white-typing-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes ray-white-bounce {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        .ray-white-chat-input {
            padding: 16px;
            border-top: 1px solid #e5e7eb;
            background: white;
        }

        .ray-white-input-container {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .ray-white-message-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 24px;
            background: #f9fafb;
            font-size: 14px;
            outline: none;
            transition: all 0.2s;
        }

        .ray-white-message-input:focus {
            border-color: #1e3a8a;
            background: white;
            box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
        }

        .ray-white-send-btn {
            background: #1e3a8a;
            color: white;
            border: none;
            border-radius: 50%;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            box-shadow: 0 2px 8px rgba(30, 58, 138, 0.3);
        }

        .ray-white-send-btn:hover:not(:disabled) {
            background: #1e40af;
            transform: scale(1.05);
        }

        .ray-white-send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .ray-white-session-info {
            margin-top: 8px;
            text-align: center;
            font-size: 10px;
            color: #9ca3af;
        }

        @keyframes ray-white-spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 480px) {
            .ray-white-chat-window {
                width: calc(100vw - 40px);
                height: calc(100vh - 120px);
                bottom: 80px;
                ${POSITION.includes('right') ? 'right: 20px;' : 'left: 20px;'}
            }
        }
    `;

    // Inject styles into the page
    const styleSheet = document.createElement('style');
    styleSheet.textContent = styles;
    document.head.appendChild(styleSheet);

    // Chat Widget Class
    class RayWhiteChatWidget {
        constructor() {
            this.isOpen = false;
            this.isLoading = false;
            this.session = this.createSession();
            this.webhookUrl = WEBHOOK_URL;
            this.route = ROUTE;
            
            this.createWidget();
            this.bindEvents();
        }

        createSession() {
            const userId = `user-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
            const sessionId = `session-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
            return {
                sessionId,
                userId,
                startTime: new Date(),
                lastActivity: new Date()
            };
        }

        createWidget() {
            const widgetHTML = `
                <div class="ray-white-chat-widget">
                    <button class="ray-white-chat-toggle" title="Chat with Ray White AI">
                        <svg width="28" height="28" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                        </svg>
                    </button>

                    <div class="ray-white-chat-window">
                        <div class="ray-white-chat-header">
                            <div class="ray-white-chat-header-content">
                                <img src="https://raywhiteltd.com/_next/static/media/logo.60ee6487.svg" 
                                     alt="Ray White Logo" class="ray-white-chat-logo">
                                <div class="ray-white-chat-title">Ray White AI</div>
                            </div>
                            <button class="ray-white-close-btn" title="Close chat">
                                <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                </svg>
                            </button>
                        </div>

                        <div class="ray-white-chat-messages">
                            <div class="ray-white-message bot">
                                <div class="ray-white-message-bubble">
                                    Hello! I'm Ray White AI assistant. How can I help you with your property needs today?
                                </div>
                            </div>
                        </div>

                        <div class="ray-white-chat-input">
                            <div class="ray-white-input-container">
                                <input type="text" class="ray-white-message-input" 
                                       placeholder="Ask about properties, locations, or services...">
                                <button class="ray-white-send-btn" title="Send message">
                                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                                    </svg>
                                </button>
                            </div>
                            <div class="ray-white-session-info">
                                Session: ${this.session.sessionId.split('-')[1]}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', widgetHTML);
            
            // Get references to elements
            this.widget = document.querySelector('.ray-white-chat-widget');
            this.toggleBtn = this.widget.querySelector('.ray-white-chat-toggle');
            this.chatWindow = this.widget.querySelector('.ray-white-chat-window');
            this.closeBtn = this.widget.querySelector('.ray-white-close-btn');
            this.messagesContainer = this.widget.querySelector('.ray-white-chat-messages');
            this.messageInput = this.widget.querySelector('.ray-white-message-input');
            this.sendBtn = this.widget.querySelector('.ray-white-send-btn');
        }

        bindEvents() {
            this.toggleBtn.addEventListener('click', () => this.toggleChat());
            this.closeBtn.addEventListener('click', () => this.closeChat());
            this.sendBtn.addEventListener('click', () => this.sendMessage());
            this.messageInput.addEventListener('keydown', (e) => this.handleKeyDown(e));
            this.messageInput.addEventListener('input', () => this.updateSendButton());
        }

        toggleChat() {
            this.isOpen = !this.isOpen;
            this.chatWindow.classList.toggle('open', this.isOpen);
            
            if (this.isOpen) {
                this.messageInput.focus();
            }
        }

        closeChat() {
            this.isOpen = false;
            this.chatWindow.classList.remove('open');
        }

        addMessage(text, isUser = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `ray-white-message ${isUser ? 'user' : 'bot'}`;
            
            const bubbleDiv = document.createElement('div');
            bubbleDiv.className = 'ray-white-message-bubble';
            bubbleDiv.textContent = text;
            
            messageDiv.appendChild(bubbleDiv);
            this.messagesContainer.appendChild(messageDiv);
            
            // Scroll to bottom
            this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
        }

        showTypingIndicator() {
            const typingDiv = document.createElement('div');
            typingDiv.id = 'ray-white-typing-indicator';
            typingDiv.className = 'ray-white-message bot';
            typingDiv.innerHTML = `
                <div class="ray-white-message-bubble">
                    <div class="ray-white-typing-indicator">
                        <span>Ray White AI is typing</span>
                        <div class="ray-white-typing-dots">
                            <div class="ray-white-typing-dot"></div>
                            <div class="ray-white-typing-dot"></div>
                            <div class="ray-white-typing-dot"></div>
                        </div>
                    </div>
                </div>
            `;
            this.messagesContainer.appendChild(typingDiv);
            this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
        }

        hideTypingIndicator() {
            const typingIndicator = document.getElementById('ray-white-typing-indicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }

        async sendToN8n(message) {
            try {
                this.isLoading = true;
                this.updateSendButton();
                this.showTypingIndicator();
                
                const payload = [{
                    action: "sendMessage",
                    sessionId: this.session.sessionId,
                    route: this.route,
                    chatInput: message,
                    metadata: {
                        timestamp: new Date().toISOString(),
                        userId: this.session.userId,
                        sessionStartTime: this.session.startTime.toISOString()
                    }
                }];

                console.log('Ray White Chat: Sending to n8n:', { url: this.webhookUrl, payload });

                const response = await fetch(this.webhookUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(payload)
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const responseText = await response.text();
                let botResponseText = '';

                try {
                    const data = JSON.parse(responseText);
                    if (Array.isArray(data) && data.length > 0 && data[0].output) {
                        botResponseText = data[0].output;
                    } else if (data.output) {
                        botResponseText = data.output;
                    } else {
                        botResponseText = "I received an unexpected response format. Please try again.";
                    }
                } catch (jsonError) {
                    botResponseText = responseText.trim();
                }

                this.hideTypingIndicator();

                if (botResponseText) {
                    this.addMessage(botResponseText, false);
                } else {
                    this.addMessage("I received an empty response. Please try again.", false);
                }

            } catch (error) {
                console.error('Ray White Chat: Error sending message to n8n:', error);
                this.hideTypingIndicator();
                this.addMessage("Sorry, I'm having trouble connecting right now. Please try again later.", false);
            } finally {
                this.isLoading = false;
                this.updateSendButton();
                this.session.lastActivity = new Date();
            }
        }

        updateSendButton() {
            this.sendBtn.disabled = this.isLoading || this.messageInput.value.trim() === '';
            this.messageInput.disabled = this.isLoading;
            
            if (this.isLoading) {
                this.sendBtn.innerHTML = `
                    <div style="width: 20px; height: 20px; border: 2px solid white; border-top: 2px solid transparent; border-radius: 50%; animation: ray-white-spin 1s linear infinite;"></div>
                `;
            } else {
                this.sendBtn.innerHTML = `
                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                    </svg>
                `;
            }
        }

        async sendMessage() {
            const message = this.messageInput.value.trim();
            
            if (message === '' || this.isLoading) return;
            
            // Add user message
            this.addMessage(message, true);
            this.messageInput.value = '';
            this.updateSendButton();
            
            // Send to n8n
            await this.sendToN8n(message);
        }

        handleKeyDown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                this.sendMessage();
            }
        }
    }

    // Initialize the chat widget when the DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            window.rayWhiteChatWidget = new RayWhiteChatWidget();
        });
    } else {
        window.rayWhiteChatWidget = new RayWhiteChatWidget();
    }

})();
