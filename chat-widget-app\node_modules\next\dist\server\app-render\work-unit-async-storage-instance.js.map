{"version": 3, "sources": ["../../../src/server/app-render/work-unit-async-storage-instance.ts"], "sourcesContent": ["import { createAsyncLocalStorage } from './async-local-storage'\nimport type { WorkUnitAsyncStorage } from './work-unit-async-storage.external'\n\nexport const workUnitAsyncStorageInstance: WorkUnitAsyncStorage =\n  createAsyncLocalStorage()\n"], "names": ["workUnitAsyncStorageInstance", "createAsyncLocalStorage"], "mappings": ";;;;+BAGaA;;;eAAAA;;;mCAH2B;AAGjC,MAAMA,+BACXC,IAAAA,0CAAuB", "ignoreList": [0]}